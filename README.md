# 最小生成树算法实现

## 项目描述

本项目用C语言实现了两种经典的最小生成树算法：
- **Kruskal算法**：基于边的贪心算法，使用并查集避免环的形成
- **Prim算法**：基于顶点的贪心算法，逐步扩展最小生成树

## 功能特点

1. **双算法实现**：同时实现Kruskal和Prim算法，便于对比
2. **灵活输入**：支持手动输入图数据或随机生成图
3. **详细过程**：显示算法执行的详细步骤
4. **结果对比**：两种算法的结果应该相同（总权值）
5. **边界处理**：支持最多30个顶点，权值小于100

## 编译和运行

### 使用Makefile（推荐）
```bash
# 编译程序
make

# 编译并运行
make run

# 运行测试用例
make test1  # 手动输入测试
make test2  # 随机生成测试

# 清理文件
make clean
```

### 手动编译
```bash
gcc -Wall -Wextra -std=c99 -g -o mst minimum_spanning_tree.c
./mst
```

## 使用说明

1. **启动程序**：运行编译后的可执行文件
2. **输入顶点数**：输入图的顶点数量（1-30）
3. **选择输入方式**：
   - 选择1：手动输入每条边的信息
   - 选择2：程序随机生成连通图
4. **查看结果**：程序会依次显示Kruskal和Prim算法的执行过程和结果

## 测试数据示例

### 测试用例1：简单图
```
顶点数：4
边数：5
边的信息：
0 1 10
0 2 6
0 3 5
1 2 4
2 3 3
```

### 测试用例2：较复杂图
```
顶点数：6
边数：9
边的信息：
0 1 4
0 2 2
1 2 1
1 3 5
2 3 8
2 4 10
3 4 2
3 5 6
4 5 3
```

## 算法复杂度

- **Kruskal算法**：O(E log E)，其中E是边数
- **Prim算法**：O(V²)，其中V是顶点数

## 数据结构

1. **图的表示**：
   - 边数组：用于Kruskal算法
   - 邻接矩阵：用于Prim算法

2. **并查集**：用于Kruskal算法检测环

3. **优先队列**：Prim算法中选择最小权值边

## 输出格式

程序会输出：
1. 原图的所有边信息
2. Kruskal算法的执行过程和结果
3. Prim算法的执行过程和结果
4. 最小生成树的总权值

## 注意事项

1. 顶点编号从0开始
2. 图必须是连通的才能生成最小生成树
3. 随机生成的图会自动保证连通性
4. 两种算法的结果总权值应该相同

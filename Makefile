# Makefile for Minimum Spanning Tree Program

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -g
TARGET = mst
SOURCE = minimum_spanning_tree.c

# 默认目标
all: $(TARGET)

# 编译目标
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE)

# 运行程序
run: $(TARGET)
	./$(TARGET)

# 清理生成的文件
clean:
	rm -f $(TARGET) $(TARGET).exe

# 测试用例1：手动输入小图
test1: $(TARGET)
	@echo "测试用例1：手动输入的小图"
	@echo "4" | ./$(TARGET)

# 测试用例2：随机生成图
test2: $(TARGET)
	@echo "测试用例2：随机生成图"
	@echo -e "6\n2" | ./$(TARGET)

# 帮助信息
help:
	@echo "可用的make目标："
	@echo "  all     - 编译程序"
	@echo "  run     - 编译并运行程序"
	@echo "  test1   - 运行测试用例1（手动输入）"
	@echo "  test2   - 运行测试用例2（随机生成）"
	@echo "  clean   - 清理生成的文件"
	@echo "  help    - 显示此帮助信息"

.PHONY: all run clean test1 test2 help

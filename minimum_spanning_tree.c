#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>

#define MAX_VERTICES 30
#define MAX_EDGES 435  // 最多30个顶点的完全图边数 = 30*29/2
#define INF 99999

// 边的结构体
typedef struct {
    int u, v;      // 边的两个顶点
    int weight;    // 边的权值
} Edge;

// 图的结构体
typedef struct {
    int vertices;  // 顶点数
    int edges;     // 边数
    Edge edge[MAX_EDGES];  // 边数组
    int adj[MAX_VERTICES][MAX_VERTICES];  // 邻接矩阵（用于Prim算法）
} Graph;

// 并查集结构体（用于Kruskal算法）
typedef struct {
    int parent[MAX_VERTICES];
    int rank[MAX_VERTICES];
} UnionFind;

// 函数声明
void initGraph(Graph *g, int vertices);
void addEdge(Graph *g, int u, int v, int weight);
void generateRandomGraph(Graph *g, int vertices, int edgeCount);
void printGraph(Graph *g);
int compare(const void *a, const void *b);
void initUnionFind(UnionFind *uf, int n);
int find(UnionFind *uf, int x);
void unionSets(UnionFind *uf, int x, int y);
void kruskalMST(Graph *g);
void primMST(Graph *g);
int minKey(int key[], int mstSet[], int vertices);

int main() {
    Graph g;
    int vertices, edgeCount;
    int choice;
    
    printf("=== Minimum Spanning Tree Algorithm Demo ===\n");
    printf("Enter number of vertices (1-%d): ", MAX_VERTICES);
    scanf("%d", &vertices);
    
    if (vertices < 1 || vertices > MAX_VERTICES) {
        printf("Number of vertices out of range!\n");
        return 1;
    }

    printf("Choose input method:\n");
    printf("1. Manual input edges\n");
    printf("2. Random generate graph\n");
    printf("Please choose (1-2): ");
    scanf("%d", &choice);
    
    initGraph(&g, vertices);
    
    if (choice == 1) {
        printf("Enter number of edges: ");
        scanf("%d", &edgeCount);
        printf("Enter each edge (format: start end weight):\n");
        for (int i = 0; i < edgeCount; i++) {
            int u, v, weight;
            printf("Edge %d: ", i + 1);
            scanf("%d %d %d", &u, &v, &weight);
            addEdge(&g, u, v, weight);
        }
    } else {
        // Generate random connected graph
        int maxEdges = vertices * (vertices - 1) / 2;
        edgeCount = vertices + rand() % (maxEdges - vertices + 1);
        generateRandomGraph(&g, vertices, edgeCount);
    }

    printf("\nOriginal graph information:\n");
    printGraph(&g);

    printf("\n=== Kruskal Algorithm for MST ===\n");
    kruskalMST(&g);

    printf("\n=== Prim Algorithm for MST ===\n");
    primMST(&g);
    
    return 0;
}

// 初始化图
void initGraph(Graph *g, int vertices) {
    g->vertices = vertices;
    g->edges = 0;
    
    // 初始化邻接矩阵
    for (int i = 0; i < vertices; i++) {
        for (int j = 0; j < vertices; j++) {
            if (i == j) {
                g->adj[i][j] = 0;
            } else {
                g->adj[i][j] = INF;
            }
        }
    }
}

// 添加边
void addEdge(Graph *g, int u, int v, int weight) {
    if (u < 0 || u >= g->vertices || v < 0 || v >= g->vertices) {
        printf("Vertex number out of range!\n");
        return;
    }
    
    // 添加到边数组
    g->edge[g->edges].u = u;
    g->edge[g->edges].v = v;
    g->edge[g->edges].weight = weight;
    g->edges++;
    
    // 更新邻接矩阵（无向图）
    g->adj[u][v] = weight;
    g->adj[v][u] = weight;
}

// 随机生成图
void generateRandomGraph(Graph *g, int vertices, int edgeCount) {
    srand(time(NULL));
    
    // 首先生成一个生成树确保连通性
    for (int i = 1; i < vertices; i++) {
        int parent = rand() % i;
        int weight = rand() % 99 + 1;
        addEdge(g, parent, i, weight);
    }
    
    // 添加剩余的边
    while (g->edges < edgeCount) {
        int u = rand() % vertices;
        int v = rand() % vertices;
        int weight = rand() % 99 + 1;
        
        if (u != v && g->adj[u][v] == INF) {
            addEdge(g, u, v, weight);
        }
    }
}

// 打印图信息
void printGraph(Graph *g) {
    printf("顶点数: %d, 边数: %d\n", g->vertices, g->edges);
    printf("所有边:\n");
    for (int i = 0; i < g->edges; i++) {
        printf("边 (%d, %d): 权值 = %d\n", 
               g->edge[i].u, g->edge[i].v, g->edge[i].weight);
    }
}

// 边权值比较函数（用于排序）
int compare(const void *a, const void *b) {
    Edge *edgeA = (Edge *)a;
    Edge *edgeB = (Edge *)b;
    return edgeA->weight - edgeB->weight;
}

// 初始化并查集
void initUnionFind(UnionFind *uf, int n) {
    for (int i = 0; i < n; i++) {
        uf->parent[i] = i;
        uf->rank[i] = 0;
    }
}

// 查找根节点（带路径压缩）
int find(UnionFind *uf, int x) {
    if (uf->parent[x] != x) {
        uf->parent[x] = find(uf, uf->parent[x]);
    }
    return uf->parent[x];
}

// 合并两个集合
void unionSets(UnionFind *uf, int x, int y) {
    int rootX = find(uf, x);
    int rootY = find(uf, y);
    
    if (rootX != rootY) {
        if (uf->rank[rootX] < uf->rank[rootY]) {
            uf->parent[rootX] = rootY;
        } else if (uf->rank[rootX] > uf->rank[rootY]) {
            uf->parent[rootY] = rootX;
        } else {
            uf->parent[rootY] = rootX;
            uf->rank[rootX]++;
        }
    }
}

// Kruskal算法
void kruskalMST(Graph *g) {
    Edge result[MAX_VERTICES - 1];  // 存储最小生成树的边
    int resultCount = 0;
    int totalWeight = 0;
    
    // 对边按权值排序
    qsort(g->edge, g->edges, sizeof(Edge), compare);
    
    // 初始化并查集
    UnionFind uf;
    initUnionFind(&uf, g->vertices);
    
    printf("Kruskal算法执行过程：\n");
    printf("按权值排序后的边：\n");
    for (int i = 0; i < g->edges; i++) {
        printf("边 (%d, %d): 权值 = %d\n", 
               g->edge[i].u, g->edge[i].v, g->edge[i].weight);
    }
    printf("\n选择的边：\n");
    
    // 遍历所有边
    for (int i = 0; i < g->edges && resultCount < g->vertices - 1; i++) {
        int u = g->edge[i].u;
        int v = g->edge[i].v;
        
        // 检查是否会形成环
        if (find(&uf, u) != find(&uf, v)) {
            result[resultCount] = g->edge[i];
            resultCount++;
            totalWeight += g->edge[i].weight;
            unionSets(&uf, u, v);
            printf("选择边 (%d, %d): 权值 = %d\n", u, v, g->edge[i].weight);
        } else {
            printf("跳过边 (%d, %d): 权值 = %d (会形成环)\n", u, v, g->edge[i].weight);
        }
    }
    
    printf("\nKruskal算法结果：\n");
    printf("最小生成树的边：\n");
    for (int i = 0; i < resultCount; i++) {
        printf("边 (%d, %d): 权值 = %d\n", 
               result[i].u, result[i].v, result[i].weight);
    }
    printf("最小生成树总权值: %d\n", totalWeight);
}

// 找到最小键值的顶点
int minKey(int key[], int mstSet[], int vertices) {
    int min = INF, minIndex = -1;
    
    for (int v = 0; v < vertices; v++) {
        if (mstSet[v] == 0 && key[v] < min) {
            min = key[v];
            minIndex = v;
        }
    }
    
    return minIndex;
}

// Prim算法
void primMST(Graph *g) {
    int parent[MAX_VERTICES];   // 存储最小生成树
    int key[MAX_VERTICES];      // 存储顶点的键值
    int mstSet[MAX_VERTICES];   // 标记顶点是否在最小生成树中
    int totalWeight = 0;
    
    // 初始化
    for (int i = 0; i < g->vertices; i++) {
        key[i] = INF;
        mstSet[i] = 0;
        parent[i] = -1;
    }
    
    // 从顶点0开始
    key[0] = 0;
    
    printf("Prim算法执行过程：\n");
    
    for (int count = 0; count < g->vertices - 1; count++) {
        // 选择最小键值的顶点
        int u = minKey(key, mstSet, g->vertices);
        mstSet[u] = 1;
        
        printf("选择顶点 %d", u);
        if (parent[u] != -1) {
            printf(" (通过边 (%d, %d), 权值 = %d)", parent[u], u, key[u]);
            totalWeight += key[u];
        }
        printf("\n");
        
        // 更新相邻顶点的键值
        for (int v = 0; v < g->vertices; v++) {
            if (g->adj[u][v] != INF && mstSet[v] == 0 && g->adj[u][v] < key[v]) {
                parent[v] = u;
                key[v] = g->adj[u][v];
            }
        }
    }
    
    printf("\nPrim算法结果：\n");
    printf("最小生成树的边：\n");
    for (int i = 1; i < g->vertices; i++) {
        if (parent[i] != -1) {
            printf("边 (%d, %d): 权值 = %d\n", parent[i], i, key[i]);
        }
    }
    printf("最小生成树总权值: %d\n", totalWeight);
}
